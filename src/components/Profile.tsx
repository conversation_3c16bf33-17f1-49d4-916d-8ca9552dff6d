import React, { useState } from 'react';
import { Modal, Typography, Space, Divider } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import ImageUploadWithCrop from './ImageUploadWithCrop';
const { Title, Text } = Typography;

interface ProfileProps {
  visible: boolean;
  onClose: () => void;
}

const Profile: React.FC<ProfileProps> = ({ visible, onClose }) => {
  const [avatarUrl, setAvatarUrl] = useState<string>('');

  // 模拟用户信息
  const userInfo = {
    name: '张三',
    email: '<EMAIL>',
  };

  // 头像上传成功处理
  const handleAvatarUploadSuccess = (url: string) => {
    setAvatarUrl(url);
    // 这里可以调用API保存头像URL到后端
    console.log('头像上传成功:', url);
  };

  // 头像上传失败处理
  const handleAvatarUploadError = (error: any) => {
    console.error('头像上传失败:', error);
  };

  // 自定义上传请求（这里可以替换为实际的上传API）
  const customAvatarUpload = (options: any) => {
    const { file, onSuccess, onError } = options;

    // 模拟上传过程
    setTimeout(() => {
      if (Math.random() > 0.1) {
        // 90% 成功率
        const mockUrl = URL.createObjectURL(file);
        onSuccess({ url: mockUrl });
      } else {
        onError(new Error('上传失败'));
      }
    }, 1000);
  };

  if (!visible) return null;
  return (
    <Modal
      open={visible}
      onCancel={onClose}
      width={1000}
      className="
      [&.ant-modal_.ant-modal-close]:(inset-ie-auto ml-12px top-40px)
      [&.ant-modal_.ant-modal-content]:( px-50px pt-35px) min-h-1100px"
      footer={null}
      keyboard={false}
      maskClosable={false}
      closeIcon={
        <div className="flex items-center gap-2 hover:opacity-45">
          <ArrowLeftOutlined
            style={{ fontSize: '16px', color: 'var(--color-label)' }}
          />
          <span className="text-#B5B5B5 font-400">Back</span>
        </div>
      }
      title={
        <div className=" text-center text-white text-32px font-700">
          Profile
        </div>
      }
    >
      <div className="flex flex-col items-center pt-55px">
        {/* 头像区域 */}
        <div className="flex flex-col items-center mb-8">
          <Title level={4} className="text-white mb-4">
            头像
          </Title>
          <ImageUploadWithCrop
            isAvatar={true}
            avatarUrl={avatarUrl}
            avatarSize={120}
            userName={userInfo.name}
            onUploadSuccess={handleAvatarUploadSuccess}
            onUploadError={handleAvatarUploadError}
            customRequest={customAvatarUpload}
            aspectRatio={1}
            maxSize={5}
          />
          <Text className="text-gray-400 mt-2 text-sm">
            点击头像可以上传新头像
          </Text>
        </div>

        <Divider className="border-gray-600" />

        {/* 用户信息区域 */}
        <div className="w-full max-w-md">
          <Space direction="vertical" size="large" className="w-full">
            <div>
              <Text strong className="text-white">
                用户名
              </Text>
              <div className="mt-1 p-3 bg-gray-800 rounded border border-gray-600">
                <Text className="text-white">{userInfo.name}</Text>
              </div>
            </div>

            <div>
              <Text strong className="text-white">
                邮箱
              </Text>
              <div className="mt-1 p-3 bg-gray-800 rounded border border-gray-600">
                <Text className="text-white">{userInfo.email}</Text>
              </div>
            </div>
          </Space>
        </div>
      </div>
    </Modal>
  );
};

export default Profile;
