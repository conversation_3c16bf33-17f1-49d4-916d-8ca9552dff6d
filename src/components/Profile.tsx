import React from 'react';
import { Modal } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
interface ProfileProps {
  visible: boolean;
  onClose: () => void;
}
const Profile: React.FC<ProfileProps> = ({ visible, onClose }) => {
  if (!visible) return null;
  return (
    <Modal
      open={visible}
      onCancel={onClose}
      width={1000}
      className="
      [&.ant-modal_.ant-modal-close]:(inset-ie-auto ml-12px top-40px)
      [&.ant-modal_.ant-modal-content]:( px-50px pt-35px) min-h-1100px"
      footer={null}
      keyboard={false}
      maskClosable={false}
      closeIcon={
        <div className="flex items-center gap-2 hover:opacity-45">
          <ArrowLeftOutlined
            style={{ fontSize: '16px', color: 'var(--color-label)' }}
          />
          <span className="text-#B5B5B5 font-400">Back</span>
        </div>
      }
      title={
        <div className=" text-center text-white text-32px font-700">
          Profile
        </div>
      }
    >
      <div className="flex items-center pt-55px  justify-center">
        <div className="w-full h-100px ">1</div>
      </div>
    </Modal>
  );
};

export default Profile;
