import React, { useState } from 'react';
import { Card, Space, Typography, Divider, Button, message } from 'antd';
import ImageUploadWithCrop from './ImageUploadWithCrop';

const { Title, Text } = Typography;

const ImageUploadExample: React.FC = () => {
  const [avatarUrl, setAvatarUrl] = useState<string>('');
  const [imageUrl, setImageUrl] = useState<string>('');

  // 模拟上传成功处理
  const handleUploadSuccess = (url: string, type: 'avatar' | 'image') => {
    if (type === 'avatar') {
      setAvatarUrl(url);
      message.success('头像上传成功！');
    } else {
      setImageUrl(url);
      message.success('图片上传成功！');
    }
  };

  // 模拟上传失败处理
  const handleUploadError = (error: any) => {
    console.error('上传失败:', error);
    message.error('上传失败，请重试！');
  };

  // 自定义上传请求（模拟）
  const customRequest = (options: any) => {
    const { file, onSuccess, onError } = options;
    
    // 模拟上传过程
    setTimeout(() => {
      if (Math.random() > 0.1) { // 90% 成功率
        const mockUrl = URL.createObjectURL(file);
        onSuccess({ url: mockUrl });
      } else {
        onError(new Error('模拟上传失败'));
      }
    }, 1000);
  };

  const resetAvatar = () => {
    setAvatarUrl('');
  };

  const resetImage = () => {
    setImageUrl('');
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <Title level={2}>图片上传裁切组件示例</Title>
      
      <Space direction="vertical" size="large" className="w-full">
        {/* 头像上传示例 */}
        <Card title="头像上传（圆形裁切）" className="w-full">
          <Space direction="vertical" size="middle">
            <Text>
              支持圆形头像上传，点击头像可以上传新图片。如果没有头像，会显示用户名首字母。
            </Text>
            
            <div className="flex items-center gap-4">
              <ImageUploadWithCrop
                isAvatar={true}
                avatarUrl={avatarUrl}
                avatarSize={80}
                userName="张三"
                onUploadSuccess={(url) => handleUploadSuccess(url, 'avatar')}
                onUploadError={handleUploadError}
                customRequest={customRequest}
                aspectRatio={1} // 1:1 比例
                maxSize={5}
              />
              <div>
                <Text strong>当前头像URL:</Text>
                <br />
                <Text code>{avatarUrl || '暂无头像'}</Text>
                <br />
                <Button size="small" onClick={resetAvatar} className="mt-2">
                  重置头像
                </Button>
              </div>
            </div>
          </Space>
        </Card>

        <Divider />

        {/* 普通图片上传示例 */}
        <Card title="普通图片上传（方形裁切）" className="w-full">
          <Space direction="vertical" size="middle">
            <Text>
              支持普通图片上传，可以自定义裁切比例。
            </Text>
            
            <div className="flex items-start gap-4">
              <ImageUploadWithCrop
                isAvatar={false}
                onUploadSuccess={(url) => handleUploadSuccess(url, 'image')}
                onUploadError={handleUploadError}
                customRequest={customRequest}
                aspectRatio={16/9} // 16:9 比例
                maxSize={10}
                className="w-64"
              />
              <div>
                <Text strong>当前图片URL:</Text>
                <br />
                <Text code>{imageUrl || '暂无图片'}</Text>
                <br />
                <Button size="small" onClick={resetImage} className="mt-2">
                  重置图片
                </Button>
              </div>
            </div>
          </Space>
        </Card>

        <Divider />

        {/* 不同尺寸头像示例 */}
        <Card title="不同尺寸头像示例" className="w-full">
          <Space direction="vertical" size="middle">
            <Text>展示不同尺寸的头像组件</Text>
            
            <Space size="large" align="center">
              <div className="text-center">
                <ImageUploadWithCrop
                  isAvatar={true}
                  avatarSize={40}
                  userName="小明"
                  onUploadSuccess={(url) => console.log('小头像:', url)}
                  customRequest={customRequest}
                />
                <div className="mt-2 text-sm">小尺寸 (40px)</div>
              </div>
              
              <div className="text-center">
                <ImageUploadWithCrop
                  isAvatar={true}
                  avatarSize={60}
                  userName="李华"
                  onUploadSuccess={(url) => console.log('中头像:', url)}
                  customRequest={customRequest}
                />
                <div className="mt-2 text-sm">中尺寸 (60px)</div>
              </div>
              
              <div className="text-center">
                <ImageUploadWithCrop
                  isAvatar={true}
                  avatarSize={100}
                  userName="王五"
                  onUploadSuccess={(url) => console.log('大头像:', url)}
                  customRequest={customRequest}
                />
                <div className="mt-2 text-sm">大尺寸 (100px)</div>
              </div>
            </Space>
          </Space>
        </Card>

        <Divider />

        {/* 功能说明 */}
        <Card title="组件功能说明" className="w-full">
          <Space direction="vertical" size="small">
            <Text>✅ 支持图片上传前裁切</Text>
            <Text>✅ 支持圆形头像模式</Text>
            <Text>✅ 支持头像回显</Text>
            <Text>✅ 支持默认文字头像</Text>
            <Text>✅ 支持自定义裁切比例</Text>
            <Text>✅ 支持文件大小限制</Text>
            <Text>✅ 支持文件类型限制</Text>
            <Text>✅ 支持自定义上传请求</Text>
            <Text>✅ 支持禁用状态</Text>
            <Text>✅ 支持自定义样式</Text>
          </Space>
        </Card>
      </Space>
    </div>
  );
};

export default ImageUploadExample;
