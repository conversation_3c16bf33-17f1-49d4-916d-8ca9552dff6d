import React, { useState, useRef, useCallback } from 'react';
import { Upload, Modal, Button, Avatar, message } from 'antd';
import {
  PlusOutlined,
  LoadingOutlined,
  CameraOutlined,
} from '@ant-design/icons';
import ReactCrop, { type Crop, type PixelCrop } from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';
import './ImageUploadWithCrop.css';

export interface ImageUploadWithCropProps {
  /** 上传接口地址 */
  action?: string;
  /** 是否为圆形头像模式 */
  isAvatar?: boolean;
  /** 头像地址（用于回显） */
  avatarUrl?: string;
  /** 头像大小（仅在isAvatar为true时生效） */
  avatarSize?: number;
  /** 用户名（用于默认头像显示） */
  userName?: string;
  /** 上传成功回调 */
  onUploadSuccess?: (url: string) => void;
  /** 上传失败回调 */
  onUploadError?: (error: any) => void;
  /** 自定义上传请求 */
  customRequest?: (options: any) => void;
  /** 裁切比例 (width/height) */
  aspectRatio?: number;
  /** 最大文件大小（MB） */
  maxSize?: number;
  /** 支持的文件类型 */
  accept?: string;
  /** 是否禁用 */
  disabled?: boolean;
  /** 自定义样式类名 */
  className?: string;
}

const ImageUploadWithCrop: React.FC<ImageUploadWithCropProps> = ({
  action,
  isAvatar = false,
  avatarUrl,
  avatarSize = 100,
  userName,
  onUploadSuccess,
  onUploadError,
  customRequest,
  aspectRatio = 1,
  maxSize = 2,
  accept = 'image/*',
  disabled = false,
  className,
}) => {
  const [loading, setLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState<string>(avatarUrl || '');
  const [cropModalVisible, setCropModalVisible] = useState(false);
  const [imageSrc, setImageSrc] = useState<string>('');
  const [crop, setCrop] = useState<Crop>({
    unit: '%',
    width: 90,
    height: 90,
    x: 5,
    y: 5,
  });
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>();
  const imgRef = useRef<HTMLImageElement>(null);
  const fileRef = useRef<File>();

  // 获取base64
  const getBase64 = (img: File, callback: (url: string) => void) => {
    const reader = new FileReader();
    reader.addEventListener('load', () => callback(reader.result as string));
    reader.readAsDataURL(img);
  };

  // 文件上传前验证
  const beforeUpload = (file: File) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('只能上传图片文件！');
      return false;
    }
    const isLtMaxSize = file.size / 1024 / 1024 < maxSize;
    if (!isLtMaxSize) {
      message.error(`图片大小不能超过 ${maxSize}MB！`);
      return false;
    }

    // 保存文件引用并显示裁切模态框
    fileRef.current = file;
    getBase64(file, url => {
      setImageSrc(url);
      setCropModalVisible(true);
    });

    return false; // 阻止自动上传
  };

  // 创建裁切后的canvas
  const getCroppedImg = useCallback(
    (image: HTMLImageElement, crop: PixelCrop): Promise<Blob> => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        throw new Error('No 2d context');
      }

      const scaleX = image.naturalWidth / image.width;
      const scaleY = image.naturalHeight / image.height;

      canvas.width = crop.width;
      canvas.height = crop.height;

      ctx.drawImage(
        image,
        crop.x * scaleX,
        crop.y * scaleY,
        crop.width * scaleX,
        crop.height * scaleY,
        0,
        0,
        crop.width,
        crop.height
      );

      return new Promise(resolve => {
        canvas.toBlob(
          blob => {
            if (blob) {
              resolve(blob);
            }
          },
          'image/jpeg',
          0.9
        );
      });
    },
    []
  );

  // 确认裁切
  const handleCropConfirm = async () => {
    if (!completedCrop || !imgRef.current) {
      return;
    }

    try {
      setLoading(true);
      const croppedImageBlob = await getCroppedImg(
        imgRef.current,
        completedCrop
      );

      // 创建新的File对象
      const croppedFile = new File(
        [croppedImageBlob],
        fileRef.current?.name || 'cropped-image.jpg',
        {
          type: 'image/jpeg',
        }
      );

      // 如果有自定义上传方法，使用自定义方法
      if (customRequest) {
        const options = {
          file: croppedFile,
          onSuccess: (response: any) => {
            const url = response?.url || URL.createObjectURL(croppedImageBlob);
            setImageUrl(url);
            onUploadSuccess?.(url);
            setLoading(false);
            setCropModalVisible(false);
            message.success('上传成功！');
          },
          onError: (error: any) => {
            onUploadError?.(error);
            setLoading(false);
            message.error('上传失败！');
          },
        };
        customRequest(options);
      } else {
        // 默认处理：生成预览URL
        const url = URL.createObjectURL(croppedImageBlob);
        setImageUrl(url);
        onUploadSuccess?.(url);
        setLoading(false);
        setCropModalVisible(false);
        message.success('图片处理成功！');
      }
    } catch (error) {
      console.error('裁切失败:', error);
      message.error('图片裁切失败！');
      setLoading(false);
    }
  };

  // 取消裁切
  const handleCropCancel = () => {
    setCropModalVisible(false);
    setImageSrc('');
    fileRef.current = undefined;
  };

  // 渲染上传按钮
  const renderUploadButton = () => {
    if (isAvatar) {
      return (
        <div className={`relative inline-block ${className || ''}`}>
          {imageUrl ? (
            <Avatar
              size={avatarSize}
              src={imageUrl}
              className="cursor-pointer hover:opacity-80 transition-opacity"
            />
          ) : (
            <Avatar
              size={avatarSize}
              className="cursor-pointer hover:opacity-80 transition-opacity bg-gray-200"
            >
              {userName ? userName.charAt(0).toUpperCase() : <CameraOutlined />}
            </Avatar>
          )}
          {!disabled && (
            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-full opacity-0 hover:opacity-100 transition-opacity cursor-pointer">
              {loading ? (
                <LoadingOutlined className="text-white text-lg" />
              ) : (
                <CameraOutlined className="text-white text-lg" />
              )}
            </div>
          )}
        </div>
      );
    }

    return (
      <div className={`upload-button ${className || ''}`}>
        {imageUrl ? (
          <img
            src={imageUrl}
            alt="uploaded"
            style={{ width: '100%', height: '100%', objectFit: 'cover' }}
          />
        ) : (
          <div className="flex flex-col items-center justify-center h-32 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-400 transition-colors cursor-pointer">
            {loading ? <LoadingOutlined /> : <PlusOutlined />}
            <div className="mt-2 text-sm text-gray-600">
              {loading ? '上传中...' : '点击上传图片'}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <>
      <Upload
        name="image"
        listType="picture-card"
        className={isAvatar ? 'avatar-uploader' : 'image-uploader'}
        showUploadList={false}
        action={action}
        beforeUpload={beforeUpload}
        accept={accept}
        disabled={disabled || loading}
      >
        {renderUploadButton()}
      </Upload>

      <Modal
        title="裁切图片"
        open={cropModalVisible}
        onOk={handleCropConfirm}
        onCancel={handleCropCancel}
        okText="确认"
        cancelText="取消"
        confirmLoading={loading}
        width={600}
        destroyOnClose
      >
        {imageSrc && (
          <div className="flex justify-center">
            <ReactCrop
              crop={crop}
              onChange={(_, percentCrop) => setCrop(percentCrop)}
              onComplete={c => setCompletedCrop(c)}
              aspect={aspectRatio}
              circularCrop={isAvatar}
            >
              <img
                ref={imgRef}
                alt="Crop me"
                src={imageSrc}
                style={{ maxHeight: '400px', maxWidth: '100%' }}
                onLoad={() => {
                  // 图片加载完成后设置默认裁切区域
                  if (imgRef.current) {
                    const { width, height } = imgRef.current;
                    const size = Math.min(width, height) * 0.8;
                    const x = (width - size) / 2;
                    const y = (height - size) / 2;

                    const newCrop: Crop = {
                      unit: 'px',
                      width: size,
                      height: size,
                      x,
                      y,
                    };
                    setCrop(newCrop);
                  }
                }}
              />
            </ReactCrop>
          </div>
        )}
      </Modal>
    </>
  );
};

export default ImageUploadWithCrop;
