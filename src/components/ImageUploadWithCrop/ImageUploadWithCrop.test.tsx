import React from 'react';
import { render, screen } from '@testing-library/react';
import ImageUploadWithCrop from './ImageUploadWithCrop';

// Mock react-image-crop
jest.mock('react-image-crop', () => ({
  __esModule: true,
  default: ({ children }: { children: React.ReactNode }) => <div data-testid="react-crop">{children}</div>,
}));

describe('ImageUploadWithCrop', () => {
  it('renders avatar mode correctly', () => {
    render(
      <ImageUploadWithCrop
        isAvatar={true}
        userName="Test User"
        onUploadSuccess={() => {}}
      />
    );

    // Should render avatar with user initial
    expect(screen.getByText('T')).toBeInTheDocument();
  });

  it('renders with avatar URL', () => {
    render(
      <ImageUploadWithCrop
        isAvatar={true}
        avatarUrl="https://example.com/avatar.jpg"
        userName="Test User"
        onUploadSuccess={() => {}}
      />
    );

    // Should render avatar image
    const avatarImg = screen.getByRole('img');
    expect(avatarImg).toHaveAttribute('src', 'https://example.com/avatar.jpg');
  });

  it('renders normal upload mode correctly', () => {
    render(
      <ImageUploadWithCrop
        isAvatar={false}
        onUploadSuccess={() => {}}
      />
    );

    // Should render upload button
    expect(screen.getByText('点击上传图片')).toBeInTheDocument();
  });

  it('handles disabled state correctly', () => {
    render(
      <ImageUploadWithCrop
        isAvatar={true}
        userName="Test User"
        disabled={true}
        onUploadSuccess={() => {}}
      />
    );

    // Upload should be disabled
    const uploadElement = screen.getByRole('button');
    expect(uploadElement).toBeDisabled();
  });
});
