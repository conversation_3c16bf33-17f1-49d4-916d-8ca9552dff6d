/* 头像上传组件样式 */
.avatar-uploader .ant-upload {
  border: none !important;
  background: transparent !important;
  padding: 0 !important;
}

.avatar-uploader .ant-upload:hover {
  border: none !important;
}

/* 普通图片上传组件样式 */
.image-uploader .ant-upload {
  border: none !important;
  background: transparent !important;
  width: 100%;
  height: auto;
}

.image-uploader .ant-upload:hover {
  border: none !important;
}

/* 上传按钮样式 */
.upload-button {
  min-height: 128px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 裁切模态框样式 */
.ReactCrop {
  max-width: 100%;
}

.ReactCrop__crop-selection {
  border: 2px solid #1890ff;
}

/* 头像悬停效果 */
.avatar-hover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  cursor: pointer;
}

.avatar-hover-overlay:hover {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .upload-button {
    min-height: 100px;
  }
}
