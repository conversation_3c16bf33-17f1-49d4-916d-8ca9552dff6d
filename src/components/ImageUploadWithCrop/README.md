# ImageUploadWithCrop 图片上传裁切组件

一个基于 Ant Design 和 react-image-crop 的图片上传裁切组件，支持上传前裁切图片，特别适用于头像上传场景。

## 功能特性

- ✅ **图片上传前裁切** - 支持在上传前对图片进行裁切
- ✅ **圆形头像模式** - 专门为头像上传设计的圆形裁切模式
- ✅ **头像回显** - 支持显示已有的头像图片
- ✅ **默认文字头像** - 当没有头像时显示用户名首字母
- ✅ **自定义裁切比例** - 支持设置不同的裁切宽高比
- ✅ **文件大小限制** - 可配置最大文件大小
- ✅ **文件类型限制** - 可配置支持的文件类型
- ✅ **自定义上传请求** - 支持自定义上传逻辑
- ✅ **禁用状态** - 支持禁用上传功能
- ✅ **自定义样式** - 支持自定义组件样式

## 安装依赖

```bash
pnpm add react-image-crop
```

## 基本用法

### 头像上传模式

```tsx
import ImageUploadWithCrop from './components/ImageUploadWithCrop';

const AvatarUpload = () => {
  const handleUploadSuccess = (url: string) => {
    console.log('头像上传成功:', url);
  };

  return (
    <ImageUploadWithCrop
      isAvatar={true}
      avatarUrl="https://example.com/avatar.jpg"
      avatarSize={100}
      userName="张三"
      onUploadSuccess={handleUploadSuccess}
      aspectRatio={1} // 1:1 圆形裁切
      maxSize={5} // 最大5MB
    />
  );
};
```

### 普通图片上传模式

```tsx
import ImageUploadWithCrop from './components/ImageUploadWithCrop';

const ImageUpload = () => {
  const handleUploadSuccess = (url: string) => {
    console.log('图片上传成功:', url);
  };

  return (
    <ImageUploadWithCrop
      isAvatar={false}
      onUploadSuccess={handleUploadSuccess}
      aspectRatio={16 / 9} // 16:9 比例裁切
      maxSize={10} // 最大10MB
    />
  );
};
```

### 自定义上传请求

```tsx
const customRequest = (options: any) => {
  const { file, onSuccess, onError } = options;

  // 使用你的上传API
  uploadToServer(file)
    .then(response => {
      onSuccess({ url: response.data.url });
    })
    .catch(error => {
      onError(error);
    });
};

<ImageUploadWithCrop
  isAvatar={true}
  customRequest={customRequest}
  onUploadSuccess={url => console.log('上传成功:', url)}
  onUploadError={error => console.error('上传失败:', error)}
/>;
```

## API 参数

| 参数              | 说明                                 | 类型                     | 默认值      |
| ----------------- | ------------------------------------ | ------------------------ | ----------- |
| `action`          | 上传接口地址                         | `string`                 | -           |
| `isAvatar`        | 是否为圆形头像模式                   | `boolean`                | `false`     |
| `avatarUrl`       | 头像地址（用于回显）                 | `string`                 | -           |
| `avatarSize`      | 头像大小（仅在isAvatar为true时生效） | `number`                 | `100`       |
| `userName`        | 用户名（用于默认头像显示）           | `string`                 | -           |
| `onUploadSuccess` | 上传成功回调                         | `(url: string) => void`  | -           |
| `onUploadError`   | 上传失败回调                         | `(error: any) => void`   | -           |
| `customRequest`   | 自定义上传请求                       | `(options: any) => void` | -           |
| `aspectRatio`     | 裁切比例 (width/height)              | `number`                 | `1`         |
| `maxSize`         | 最大文件大小（MB）                   | `number`                 | `2`         |
| `accept`          | 支持的文件类型                       | `string`                 | `'image/*'` |
| `disabled`        | 是否禁用                             | `boolean`                | `false`     |
| `className`       | 自定义样式类名                       | `string`                 | -           |

## 使用场景

### 1. 用户头像上传

在用户个人资料页面中使用，支持圆形头像裁切和回显。

### 2. 商品图片上传

在商品管理页面中使用，支持自定义比例的图片裁切。

### 3. 文章封面上传

在内容管理系统中使用，支持16:9等特定比例的封面图片裁切。

## 注意事项

1. **依赖要求**: 需要安装 `react-image-crop` 依赖
2. **样式引入**: 组件会自动引入 `react-image-crop/dist/ReactCrop.css` 样式
3. **文件大小**: 建议设置合理的文件大小限制，避免上传过大的图片
4. **浏览器兼容性**: 依赖现代浏览器的 Canvas API 和 File API
5. **内存管理**: 组件会自动清理创建的 Object URL，避免内存泄漏

## 自定义样式

组件支持通过 `className` 属性自定义样式：

```tsx
<ImageUploadWithCrop
  className="my-custom-upload"
  // ... 其他属性
/>
```

```css
.my-custom-upload {
  /* 自定义样式 */
}
```

## 在Profile组件中的使用

组件已经集成到 `Profile.tsx` 中，展示了如何在用户个人资料页面中使用头像上传功能：

```tsx
// 在Profile组件中的使用示例
<ImageUploadWithCrop
  isAvatar={true}
  avatarUrl={avatarUrl}
  avatarSize={120}
  userName={userInfo.name}
  onUploadSuccess={handleAvatarUploadSuccess}
  onUploadError={handleAvatarUploadError}
  customRequest={customAvatarUpload}
  aspectRatio={1}
  maxSize={5}
/>
```

## 示例

查看 `ImageUploadExample.tsx` 文件获取完整的使用示例。

## 技术实现

### 核心技术栈

- **React**: 组件基础框架
- **Ant Design**: UI组件库，提供Upload、Modal、Avatar等组件
- **react-image-crop**: 图片裁切功能
- **Canvas API**: 图片处理和裁切实现
- **File API**: 文件读取和处理

### 关键功能实现

1. **图片裁切**: 使用react-image-crop库实现交互式图片裁切
2. **文件处理**: 通过Canvas API将裁切区域转换为Blob对象
3. **头像模式**: 支持圆形裁切和圆形显示
4. **文件验证**: 支持文件类型和大小验证
5. **自定义上传**: 支持自定义上传逻辑，可以集成任何后端API

### 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

需要支持以下Web API：

- Canvas API
- File API
- FileReader API
- Blob API
