import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuthStore } from '@/store/authStore';
import { getDefaultPageByRole } from '@/utils/roleUtils';

/**
 * 基于角色的重定向组件
 * 根据用户角色重定向到对应的默认页面
 */
const RoleBasedRedirect: React.FC = () => {
  const { user, isAuthenticated } = useAuthStore();

  // 如果未登录，重定向到登录页
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // 根据用户角色获取默认页面
  const defaultPage = getDefaultPageByRole(user);
  
  return <Navigate to={defaultPage} replace />;
};

export default RoleBasedRedirect;
