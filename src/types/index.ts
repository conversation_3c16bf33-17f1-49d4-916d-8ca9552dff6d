import type { Permission } from '@/router/routeMap';

// 导入 API 相关类型
export * from './api';

// 注册用户表单数据类型 - 与API保持一致
export interface RegisterFormData {
  email?: string;
  alias?: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  addressLine1?: string;
  addressLine2?: string;
  countryCode?: string;
  stateProvince?: string;
  region?: string; // 新增 region 字段，与 stateProvince 功能相同但更语义化
  countryRegion?: (string | number)[];
  postalZipCode?: string;
  password?: string;
  avatarUrl?: string;
  stageName?: string;
  bio?: string;
  defaultRoleId?: string;
}

// 登录表单数据类型, 需要转换成api.ts中的LoginRequest
export interface LoginFormData {
  email: string; // 邮箱
  password?: string; // 密码
  emailCode?: string; // 邮箱验证码
}

// 用户角色枚举 - 与API角色ID保持一致
export enum UserRole {
  INVESTOR = 'account.role.investor',
  MUSICIAN = 'account.role.musician',
  ADMIN = 'account.role.admin',
}

// 用户信息类型 - 与API响应保持一致
export interface User {
  accountId: string;
  email: string;
  alias: string;
  firstName: string;
  lastName: string;
  mobile?: string;
  addressLine1?: string;
  addressLine2?: string;
  stateProvince?: string;
  countryCode?: string;
  postalZipCode?: string;
  avatarUrl?: string;
  stageName?: string;
  bio?: string;
  displayName: string;
  roles: Array<{
    id: string;
    name: string;
    realm: number;
  }>;
}

// 音频文件类型
export interface AudioFile {
  id: string;
  title: string;
  description: string;
  url: string;
  thumbnailUrl?: string;
  duration: number; // 秒
  fileSize: number; // 字节
  format: string;
  price: number;
  category: string;
  tags: string[];
  creatorId: string;
  creator: User;
  downloads: number;
  rating: number;
  createdAt: string;
  updatedAt: string;
}

// API 响应类型 - 保持向后兼容，但推荐使用 api.ts 中的 ApiResponse
export interface ApiResponse<T = any> {
  code: number;
  message?: string;
  data?: T;
  body?: T; // 新增 body 字段以匹配实际API响应
  success?: boolean;
}

// 分页响应类型
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 表单验证错误类型
export interface FormError {
  field: string;
  message: string;
}

// 通用选项类型
export interface Option {
  label: string;
  value: string | number;
  disabled?: boolean;
}

// 文件上传类型
export interface UploadFile {
  uid: string;
  name: string;
  status: 'uploading' | 'done' | 'error';
  response?: any;
  url?: string;
  size?: number;
  type?: string;
}

// 搜索筛选类型
export interface SearchFilters {
  keyword?: string;
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  duration?: {
    min?: number;
    max?: number;
  };
  rating?: number;
  sortBy?:
    | 'newest'
    | 'oldest'
    | 'price_low'
    | 'price_high'
    | 'popular'
    | 'rating';
}

// 路由参数类型
export interface RouteParams {
  id?: string;
  page?: string;
  category?: string;
  [key: string]: string | undefined;
}
