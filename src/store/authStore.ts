import { create } from 'zustand';
import { persist, devtools } from 'zustand/middleware';
import type {} from '@redux-devtools/extension'; // devtools 类型支持
import type { User } from '@/types';
import type { LoginRequest } from '@/types';
import type { Permission } from '@/router/routeMap';
import { api } from '@/services';

export type { User };

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean; // 登录状态
  login: (loginData: LoginRequest) => Promise<void>;
  fetchUserInfo: () => void;
  initializeAuth: () => void;
  logout: () => void;
}

export const useAuthStore = create<AuthState>()(
  devtools(
    persist(
      (set, get) => ({
        user: null,
        token: null,
        isAuthenticated: false,
        login: async loginData => {
          try {
            // 1. 获取token和基本用户信息
            const result = await api.auth.login(loginData);
            if (result.code === 20 || result.code === 200) {
              const authData = result.body;

              // 构建用户对象
              const user: User = {
                accountId: authData.accountId,
                email: '', // 登录接口不返回email，需要从profile接口获取
                alias: authData.alias,
                firstName: '',
                lastName: '',
                displayName: authData.displayName,
                avatarUrl: authData.avatarUrl,
                stageName: authData.stageName,
                roles: authData.roles,
              };

              set({
                token: authData.token,
                isAuthenticated: true,
                user: user,
              });
              localStorage.setItem('token', authData.token);

              // 2. 尝试获取完整用户信息（可选）
              try {
                get().fetchUserInfo();
                console.log('获取完整用户数据成功');
              } catch (userInfoError) {
                console.error(
                  '获取完整用户信息失败，但登录成功:',
                  userInfoError
                );
                // 不抛出错误，允许登录继续
              }
            }
          } catch (error) {
            console.error('登录失败----', error);
            throw error;
          }
        },
        // 获取用户信息
        fetchUserInfo: () => {
          try {
            // 如果需要获取更多用户信息，可以在这里实现
            console.log('fetchUserInfo: 不再需要获取权限信息');
          } catch (error) {
            console.error('获取用户信息失败:', error);
            throw error;
          }
        },
        initializeAuth: () => {
          const token = localStorage.getItem('token');
          if (token) {
            set({ token, isAuthenticated: true });
            get().fetchUserInfo();
          } else {
            // Token 无效，清除登录
            get().logout();
          }
        },
        logout: () => {
          set({
            user: null,
            token: null,
            isAuthenticated: false,
          });
          localStorage.removeItem('token');
        },
      }),
      {
        name: 'auth-store', // localStorage持久化的名称
      }
    ),
    {
      name: 'AuthStore', // DevTools 中显示的名称
    }
  )
);
